{% comment %}
  Multicolumn Section
  Displays content in multiple columns with images and text
{% endcomment %}

<div class="multicolumn color-{{ section.settings.color_scheme }} gradient{% unless section.settings.background_style == 'none' and settings.text_boxes_border_thickness == 0 or settings.text_boxes_shadow_opacity == 0 %} background-{{ section.settings.background_style }}{% endunless %}{% if section.settings.title == blank %} no-heading{% endif %}">
  <div class="page-width section-{{ section.id }}-padding">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
        <h2 class="title inline-richtext {{ section.settings.heading_size }}">
          {{ section.settings.title }}
        </h2>
        {%- if section.settings.button_label != blank and show_mobile_slider -%}
          <a href="{{ section.settings.button_link }}" class="link underlined-link large-up-hide">
            {{ section.settings.button_label | escape }}
          </a>
        {%- endif -%}
      </div>
    {%- endunless -%}
    <slider-component class="slider-mobile-gutter">
      <ul class="multicolumn-list contains-content-container grid grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% if show_mobile_slider %} slider slider--tablet grid--peek{% endif %}"
          id="Slider-{{ section.id }}"
          role="list">
        {%- liquid
          assign highest_ratio = 0
          for block in section.blocks
            if block.settings.image.aspect_ratio > highest_ratio
              assign highest_ratio = block.settings.image.aspect_ratio
            endif
          endfor
        -%}
        {%- for block in section.blocks -%}
          {%- assign empty_column = '' -%}
          {%- if block.settings.image == blank and block.settings.title == blank and block.settings.text == blank and block.settings.link_label == blank -%}
            {%- assign empty_column = ' multicolumn-list__item--empty' -%}
          {%- endif -%}

          <li id="Slide-{{ section.id }}-{{ forloop.index }}" class="multicolumn-list__item grid__item{% if show_mobile_slider %} slider__slide{% endif %}{% if section.settings.swipe_on_mobile %} slider__slide--full-width{% endif %}{{ empty_column }}"{{ block.shopify_attributes }}>
            <div class="multicolumn-card content-container">
              {%- if block.settings.image != blank -%}
                {% if section.settings.image_ratio == 'adapt' or section.settings.image_ratio == 'circle' %}
                  {% assign spaced_image = true %}
                {% endif %}
                <div class="multicolumn-card__image-wrapper multicolumn-card__image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}">
                  <div class="media media--transparent media--{{ section.settings.image_ratio }}"
                    {% if section.settings.image_ratio == 'adapt' %} style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"{% endif %}
                  >
                    {%- capture sizes -%}
                      (min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %}, (min-width: 750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %}, calc(100vw - 30px)
                    {%- endcapture -%}
                    {{ block.settings.image | image_url: width: 550 | image_tag:
                      loading: 'lazy',
                      sizes: sizes,
                      widths: '275, 550, 710, 1420',
                      class: 'multicolumn-card__image'
                    }}
                  </div>
                </div>
              {%- endif -%}
              <div class="multicolumn-card__info">
                {%- if block.settings.title != blank -%}
                  <h3 class="inline-richtext">{{ block.settings.title }}</h3>
                {%- endif -%}
                {%- if block.settings.text != blank -%}
                  <div class="rte">{{ block.settings.text }}</div>
                {%- endif -%}
                {%- if block.settings.link_label != blank -%}
                  <a class="link animate-arrow"
                    {% if block.settings.link == blank %}
                      role="button" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.link }}"
                    {% endif %}
                  >
                    {{- block.settings.link_label | escape -}}
                    <span class="icon-wrap">&nbsp;{% render 'icon-arrow' %}</span></a>
                {%- endif -%}
              </div>
            </div>
          </li>
        {%- endfor -%}
      </ul>

      {%- if show_mobile_slider -%}
        <div class="slider-buttons no-js-hidden medium-hide">
          <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
          <div class="slider-counter caption">
            <span class="slider-counter--current">1</span>
            <span aria-hidden="true"> / </span>
            <span class="slider-counter--total">{{ section.blocks.size }}</span>
          </div>
          <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
        </div>
      {%- endif -%}
    </slider-component>
    <div class="center{% if show_mobile_slider %} small-hide medium-hide{% endif %}">
      {%- if section.settings.button_label != blank -%}
        <a class="button"
          {% if section.settings.button_link == blank %}
            role="button" aria-disabled="true"
          {% else %}
            href="{{ section.settings.button_link }}"
          {% endif %}
        >
          {{ section.settings.button_label | escape }}
        </a>
      {%- endif -%}
    </div>
  </div>
</div>

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .multicolumn-list {
    display: grid;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .multicolumn-list__item {
    text-align: {{ section.settings.column_alignment }};
  }

  .multicolumn-card {
    position: relative;
    box-sizing: border-box;
  }

  .multicolumn-card__image-wrapper {
    margin-bottom: 1rem;
  }

  .multicolumn-card__image {
    width: 100%;
    height: auto;
  }

  .multicolumn-card__info h3 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }

  .multicolumn-card__info .rte {
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .grid--1-col-desktop {
    grid-template-columns: 1fr;
  }

  .grid--2-col-desktop {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid--3-col-desktop {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid--4-col-desktop {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid--5-col-desktop {
    grid-template-columns: repeat(5, 1fr);
  }

  @media screen and (max-width: 749px) {
    .grid--1-col-tablet-down {
      grid-template-columns: 1fr;
    }

    .grid--2-col-tablet-down {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .media--adapt {
    height: auto;
  }

  .media--square {
    aspect-ratio: 1;
  }

  .media--portrait {
    aspect-ratio: 2/3;
  }

  .media--landscape {
    aspect-ratio: 3/2;
  }

  .media--circle {
    border-radius: 50%;
    overflow: hidden;
  }

  .link {
    color: #007bff;
    text-decoration: none;
  }

  .link:hover {
    text-decoration: underline;
  }

  .button {
    display: inline-block;
    padding: 12px 24px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
  }

  .button:hover {
    background-color: #0056b3;
  }

  .center {
    text-align: center;
    margin-top: 2rem;
  }
</style>

{% schema %}
{
  "name": "Multicolumn",
  "class": "section",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Multicolumn",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "third",
          "label": "One third of column"
        },
        {
          "value": "half",
          "label": "Half of column"
        },
        {
          "value": "full",
          "label": "Full width"
        }
      ],
      "default": "full",
      "label": "Image width"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "circle",
          "label": "Circle"
        }
      ],
      "default": "adapt",
      "label": "Image ratio"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "Number of columns on desktop"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "left",
      "label": "Column alignment"
    },
    {
      "type": "select",
      "id": "background_style",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "primary",
          "label": "Primary"
        }
      ],
      "default": "primary",
      "label": "Background"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "Button label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "background-1",
      "label": "Color scheme"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "1",
      "label": "Number of columns on mobile"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "Enable swipe on mobile"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "Column",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "Column",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "Text"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "Link label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Multicolumn",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}

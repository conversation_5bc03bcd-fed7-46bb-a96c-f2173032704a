{%- liquid
  assign sticky_header = false
  if settings.enable_sticky_header
    assign sticky_header = true
  endif
-%}

<script src="{{ 'details-disclosure.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'details-modal.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'cart-notification.js' | asset_url }}" defer="defer"></script>

{%- if settings.cart_type == "drawer" -%}
  <script src="{{ 'cart-drawer.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

<{% if sticky_header %}sticky-header data-sticky-type="on-scroll-up"{% else %}div{% endif %} class="header-wrapper color-{{ section.settings.color_scheme }} gradient{% if sticky_header %} header-wrapper--border-bottom{% endif %}">
  <header class="header header--{{ section.settings.logo_position }} header--mobile-{{ section.settings.mobile_logo_position }} page-width{% if section.settings.menu != blank %} header--has-menu{% endif %}{% if section.settings.enable_country_selector or section.settings.enable_language_selector %} header--has-localizations{% endif %}{% if section.settings.enable_customer_avatar %} header--has-account{% endif %}">
    {%- if section.settings.menu != blank -%}
      <nav class="header__inline-menu">
        <details-disclosure>
          <details id="Details-menu-drawer-container" class="menu-drawer-container">
            <summary class="header__icon header__icon--menu header__icon--summary link focus-inset" aria-label="{{ 'sections.header.menu' | t }}">
              <span>
                {% render 'icon-hamburger' %}
                {% render 'icon-close' %}
              </span>
            </summary>
            <div id="menu-drawer" class="gradient menu-drawer motion-reduce" tabindex="-1">
              <div class="menu-drawer__inner-container">
                <div class="menu-drawer__navigation-container">
                  <nav class="menu-drawer__navigation">
                    <ul class="menu-drawer__menu has-submenu list-menu" role="list">
                      {%- for link in section.settings.menu.links -%}
                        <li>
                          {%- if link.links != blank -%}
                            <details id="Details-menu-drawer-menu-item-{{ forloop.index }}">
                              <summary id="HeaderDrawer-{{ link.handle }}" class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.child_active %} menu-drawer__menu-item--active{% endif %}">
                                {{ link.title | escape }}
                                {% render 'icon-arrow' %}
                                {% render 'icon-caret' %}
                              </summary>
                              <div id="link-{{ link.handle | escape }}" class="menu-drawer__submenu has-submenu gradient motion-reduce" tabindex="-1">
                                <div class="menu-drawer__inner-submenu">
                                  <button class="menu-drawer__close-button link link--text focus-inset" aria-expanded="true">
                                    {% render 'icon-arrow' %}
                                    {{ link.title | escape }}
                                  </button>
                                  <ul class="menu-drawer__menu list-menu" role="list" tabindex="-1">
                                    {%- for childlink in link.links -%}
                                      <li>
                                        <a href="{{ childlink.url }}" class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if childlink.current %} menu-drawer__menu-item--active{% endif %}"{% if childlink.current %} aria-current="page"{% endif %}>
                                          {{ childlink.title | escape }}
                                        </a>
                                      </li>
                                    {%- endfor -%}
                                  </ul>
                                </div>
                              </div>
                            </details>
                          {%- else -%}
                            <a href="{{ link.url }}" class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.current %} menu-drawer__menu-item--active{% endif %}"{% if link.current %} aria-current="page"{% endif %}>
                              {{ link.title | escape }}
                            </a>
                          {%- endif -%}
                        </li>
                      {%- endfor -%}
                    </ul>
                  </nav>
                  <div class="menu-drawer__utility-links">
                    {%- if section.settings.enable_customer_avatar -%}
                      <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="menu-drawer__account link focus-inset h5 medium-hide large-up-hide">
                        {%- if customer -%}
                          <account-icon>
                            {%- if customer.has_avatar -%}
                              {{ customer | avatar }}
                            {%- else -%}
                              {% render 'icon-account' %}
                            {%- endif -%}
                          </account-icon>
                          {{ 'customer.account_fallback' | t }}
                        {%- else -%}
                          {% render 'icon-account' %}
                          {{ 'customer.log_in' | t }}
                        {%- endif -%}
                      </a>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </details>
        </details-disclosure>
      </nav>
    {%- endif -%}

    {%- if section.settings.logo != blank -%}
      <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
        {%- assign logo_alt = section.settings.logo.alt | default: shop.name | escape -%}
        {%- assign logo_height = section.settings.logo_width | divided_by: section.settings.logo.aspect_ratio -%}
        {% capture sizes %}(max-width: {{ section.settings.logo_width | times: 2 }}px) 50vw, {{ section.settings.logo_width }}px{% endcapture %}
        {{ section.settings.logo | image_url: width: 600 | image_tag:
          class: 'header__heading-logo motion-reduce',
          widths: '50, 100, 150, 200, 250, 300, 400, 500, 600',
          height: logo_height,
          width: section.settings.logo_width,
          alt: logo_alt,
          sizes: sizes,
          preload: true
        }}
      </a>
    {%- else -%}
      <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
        <span class="h2">{{ shop.name }}</span>
      </a>
    {%- endif -%}

    {%- if section.settings.menu != blank -%}
      <nav class="header__inline-menu">
        <ul class="list-menu list-menu--inline" role="list">
          {%- for link in section.settings.menu.links -%}
            <li>
              {%- if link.links != blank -%}
                <header-menu>
                  <details id="Details-HeaderMenu-{{ forloop.index }}" class="mega-menu">
                    <summary id="HeaderMenu-{{ link.handle }}" class="header__menu-item list-menu__item link focus-inset">
                      <span {%- if link.child_active %} class="header__active-menu-item"{% endif %}>{{ link.title | escape }}</span>
                      {% render 'icon-caret' %}
                    </summary>
                    <div id="MegaMenu-Content-{{ forloop.index }}" class="mega-menu__content color-{{ section.settings.menu_color_scheme }} gradient motion-reduce global-media-settings" tabindex="-1">
                      <ul class="mega-menu__list page-width" role="list">
                        {%- for childlink in link.links -%}
                          <li>
                            <a href="{{ childlink.url }}" class="mega-menu__link mega-menu__link--level-2 link{% if childlink.current %} mega-menu__link--active{% endif %}"{% if childlink.current %} aria-current="page"{% endif %}>
                              {{ childlink.title | escape }}
                            </a>
                          </li>
                        {%- endfor -%}
                      </ul>
                    </div>
                  </details>
                </header-menu>
              {%- else -%}
                <a href="{{ link.url }}" class="header__menu-item list-menu__item link link--text focus-inset"{% if link.current %} aria-current="page"{% endif %}>
                  <span {%- if link.current %} class="header__active-menu-item"{% endif %}>{{ link.title | escape }}</span>
                </a>
              {%- endif -%}
            </li>
          {%- endfor -%}
        </ul>
      </nav>
    {%- endif -%}

    <div class="header__icons">
      <details-modal class="header__search">
        <details>
          <summary class="header__icon header__icon--search header__icon--summary link focus-inset modal__toggle" aria-haspopup="dialog" aria-label="{{ 'general.search.search' | t }}">
            <span>
              <svg class="modal__toggle-open icon icon-search" aria-hidden="true" focusable="false">
                <use href="#icon-search">
              </svg>
              <svg class="modal__toggle-close icon icon-close" aria-hidden="true" focusable="false">
                <use href="#icon-close">
              </svg>
            </span>
          </summary>
          <div class="search-modal modal__content gradient" role="dialog" aria-modal="true" aria-label="{{ 'general.search.search' | t }}">
            <div class="modal-overlay"></div>
            <div class="search-modal__content search-modal__content-bottom" tabindex="-1">
              {%- if settings.predictive_search_enabled -%}
                <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
              {%- endif -%}
                <form action="{{ routes.search_url }}" method="get" role="search" class="search search-modal__form">
                  <div class="field">
                    <input class="search__input field__input" id="Search-In-Modal" type="search" name="q" value="{{ search.terms | escape }}" placeholder="{{ 'general.search.search' | t }}" {%- if settings.predictive_search_enabled -%}role="combobox" aria-expanded="false" aria-owns="predictive-search-results-list" aria-autocomplete="list" autocorrect="off" autocomplete="off" autocapitalize="off" spellcheck="false"{%- endif -%}>
                    <label class="field__label" for="Search-In-Modal">{{ 'general.search.search' | t }}</label>
                    <input type="hidden" name="options[prefix]" value="last">
                    <button type="reset" class="reset__button field__button" aria-label="{{ 'general.search.reset' | t }}">
                      <svg class="icon icon-close" aria-hidden="true" focusable="false">
                        <use xlink:href="#icon-close">
                      </svg>
                    </button>
                    <button class="search__button field__button" aria-label="{{ 'general.search.search' | t }}">
                      <svg class="icon icon-search" aria-hidden="true" focusable="false">
                        <use href="#icon-search">
                      </svg>
                    </button>
                  </div>

                  {%- if settings.predictive_search_enabled -%}
                    <div class="predictive-search predictive-search--header" tabindex="-1" data-predictive-search>
                      <div class="predictive-search__loading-state">
                        <svg aria-hidden="true" focusable="false" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                          <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                        </svg>
                      </div>
                    </div>

                    <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
                  {%- endif -%}
                </form>
              {%- if settings.predictive_search_enabled -%}
                </predictive-search>
              {%- endif -%}
            </div>
          </div>
        </details>
      </details-modal>

      {%- if section.settings.enable_customer_avatar -%}
        <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="header__icon header__icon--account link focus-inset{% if section.settings.menu != blank %} small-hide{% endif %}">
          {%- if customer and customer.has_avatar? -%}
            {{ customer | avatar }}
          {%- else -%}
            {% render 'icon-account' %}
          {%- endif -%}
          <span class="visually-hidden">
            {%- liquid
              if customer
                echo 'customer.account_fallback' | t
              else
                echo 'customer.log_in' | t
              endif
            -%}
          </span>
        </a>
      {%- endif -%}

      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when '@app' -%}
            {% render block %}
        {%- endcase -%}
      {%- endfor -%}

      <a href="{{ routes.cart_url }}" class="header__icon header__icon--cart link focus-inset" id="cart-icon-bubble">
        {%- liquid
          if cart == empty
            render 'icon-cart-empty'
          else
            render 'icon-cart'
          endif
        -%}
        <span class="visually-hidden">{{ 'templates.cart.cart' | t }}</span>
        {%- if cart != empty -%}
          <div class="cart-count-bubble">
            {%- if cart.item_count < 100 -%}
              <span aria-hidden="true">{{ cart.item_count }}</span>
            {%- endif -%}
            <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: cart.item_count }}</span>
          </div>
        {%- endif -%}
      </a>
    </div>
  </header>
</{% if sticky_header %}sticky-header{% else %}div{% endif %}>

<cart-notification>
  <div class="cart-notification-wrapper page-width">
    <div id="cart-notification" class="cart-notification focus-inset color-{{ settings.cart_notification_color_scheme }} gradient" aria-modal="true" aria-label="{{ 'general.cart.item_added' | t }}" role="dialog" tabindex="-1">
      <div class="cart-notification__header">
        <h3 class="cart-notification__heading caption-large text-body">
          <span>{{ 'general.cart.item_added' | t }}</span>
          <svg class="icon icon-checkmark color-foreground-text" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 9" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.35.643a.5.5 0 01.006.707L4.417 8.34a.5.5 0 01-.708.006L.643 5.48a.5.5 0 01.714-.707L4 7.269l6.643-6.62a.5.5 0 01.707-.006z" fill="currentColor"/>
          </svg>
        </h3>
        <button type="button" class="cart-notification__close modal__close-button link link--text focus-inset" aria-label="{{ 'accessibility.close' | t }}">
          <svg class="icon icon-close" aria-hidden="true" focusable="false">
            <use href="#icon-close">
          </svg>
        </button>
      </div>
      <div id="cart-notification-product" class="cart-notification-product"></div>
      <div class="cart-notification__links">
        <a href="{{ routes.cart_url }}" id="cart-notification-button" class="button button--secondary button--full-width">{{ 'general.cart.view_cart' | t }}</a>
        <form action="{{ routes.cart_url }}" method="post" id="cart-notification-form">
          <button class="button button--primary button--full-width" name="add" type="submit" form="cart-notification-form">{{ 'general.cart.checkout' | t }}</button>
        </form>
        <button type="button" class="link button-label">{{ 'general.continue_shopping' | t }}</button>
      </div>
    </div>
  </div>
</cart-notification>
<style data-shopify>
  .header {
    padding: 10px 0;
  }

  .header-wrapper {
    position: relative;
    background: var(--color-background);
    border-bottom: 1px solid rgba(var(--color-text), 0.1);
  }

  .header-wrapper--border-bottom {
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  }

  @media screen and (min-width: 990px) {
    .header {
      padding: 20px 0;
    }
  }

  .header__heading-logo {
    max-width: {{ section.settings.logo_width }}px;
  }

  .header__heading-link {
    text-decoration: none;
    color: var(--color-text);
  }

  .header__heading-link:hover {
    color: var(--color-primary);
  }
</style>

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Organization",
    "name": {{ shop.name | json }},
    {% if section.settings.logo %}
      "logo": {{ section.settings.logo | image_url: width: 500 | prepend: "https:" | json }},
    {% endif %}
    "sameAs": [
      {{ settings.social_twitter_link | json }},
      {{ settings.social_facebook_link | json }},
      {{ settings.social_pinterest_link | json }},
      {{ settings.social_instagram_link | json }},
      {{ settings.social_tiktok_link | json }},
      {{ settings.social_tumblr_link | json }},
      {{ settings.social_snapchat_link | json }},
      {{ settings.social_youtube_link | json }},
      {{ settings.social_vimeo_link | json }}
    ],
    "url": {{ request.origin | append: page.url | json }}
  }
</script>

{%- if request.page_type == 'index' -%}
  {% assign potential_action_target = request.origin | append: routes.search_url | append: "?q={search_term_string}" %}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "potentialAction": {
        "@type": "SearchAction",
        "target": {{ potential_action_target | json }},
        "query-input": "required name=search_term_string"
      },
      "url": {{ request.origin | append: page.url | json }}
    }
  </script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.header.name",
  "class": "section-header",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "background-1"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.header.settings.logo.label"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 250,
      "step": 10,
      "default": 100,
      "unit": "t:sections.header.settings.logo_width.unit",
      "label": "t:sections.header.settings.logo_width.label"
    },
    {
      "type": "select",
      "id": "logo_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.header.settings.logo_position.options__2.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.header.settings.logo_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.header.settings.logo_position.options__1.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.header.settings.logo_position.options__4.label"
        }
      ],
      "default": "middle-left",
      "label": "t:sections.header.settings.logo_position.label",
      "info": "t:sections.header.settings.logo_position.info"
    },
    {
      "type": "link_list",
      "id": "menu",
      "default": "main-menu",
      "label": "t:sections.header.settings.menu.label"
    },
    {
      "type": "color_scheme",
      "id": "menu_color_scheme",
      "label": "t:sections.header.settings.menu_color_scheme.label",
      "default": "background-1"
    },
    {
      "type": "select",
      "id": "mobile_logo_position",
      "options": [
        {
          "value": "center",
          "label": "t:sections.header.settings.mobile_logo_position.options__1.label"
        },
        {
          "value": "left",
          "label": "t:sections.header.settings.mobile_logo_position.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.header.settings.mobile_logo_position.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__3.content"
    },
    {
      "type": "checkbox",
      "id": "enable_customer_avatar",
      "default": false,
      "label": "t:sections.header.settings.enable_customer_avatar.label",
      "info": "t:sections.header.settings.enable_customer_avatar.info"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__4.content"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": false,
      "label": "t:sections.header.settings.enable_country_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__5.content"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": false,
      "label": "t:sections.header.settings.enable_language_selector.label"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ]
}
{% endschema %}

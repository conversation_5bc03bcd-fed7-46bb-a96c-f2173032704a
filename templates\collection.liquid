{% comment %}
  Collection Template
  Displays products in a collection
{% endcomment %}

<div class="template-collection">
  <div class="page-width">
    
    {% if collection.description != blank %}
      <div class="collection-description rte">
        {{ collection.description }}
      </div>
    {% endif %}

    <div class="collection-header">
      <h1 class="collection-title">{{ collection.title }}</h1>
      <p class="collection-count">
        {{ 'collections.general.items_with_count' | t: count: collection.products_count }}
      </p>
    </div>

    {% if collection.products.size > 0 %}
      <div class="collection-grid">
        {% for product in collection.products %}
          <div class="product-card">
            <a href="{{ product.url }}" class="product-card__link">
              {% if product.featured_media %}
                <div class="product-card__image">
                  {{ product.featured_media | image_url: width: 300 | image_tag: 
                     alt: product.featured_media.alt | default: product.title,
                     loading: 'lazy' }}
                </div>
              {% endif %}
              
              <div class="product-card__info">
                <h3 class="product-card__title">{{ product.title }}</h3>
                <div class="product-card__price">
                  {% if product.compare_at_price > product.price %}
                    <span class="price price--on-sale">
                      {{ product.price | money }}
                    </span>
                    <span class="price price--compare">
                      {{ product.compare_at_price | money }}
                    </span>
                  {% else %}
                    <span class="price">
                      {{ product.price | money }}
                    </span>
                  {% endif %}
                </div>
              </div>
            </a>
          </div>
        {% endfor %}
      </div>
    {% else %}
      <div class="collection-empty">
        <p>{{ 'collections.general.no_matches' | t }}</p>
      </div>
    {% endif %}

  </div>
</div>

<style>
.template-collection {
  padding: 40px 0;
}

.collection-header {
  text-align: center;
  margin-bottom: 40px;
}

.collection-title {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.collection-count {
  color: #666;
  font-size: 1rem;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.product-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.product-card__link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.product-card__image {
  aspect-ratio: 1;
  overflow: hidden;
}

.product-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-card__info {
  padding: 20px;
}

.product-card__title {
  font-size: 1.2rem;
  margin-bottom: 10px;
  line-height: 1.4;
}

.product-card__price {
  font-weight: 600;
}

.price--on-sale {
  color: #e74c3c;
}

.price--compare {
  text-decoration: line-through;
  color: #999;
  margin-left: 10px;
}

.collection-empty {
  text-align: center;
  padding: 60px 0;
  color: #666;
}
</style>

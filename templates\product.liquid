{{ 'section-product.css' | asset_url | stylesheet_tag }}
{{ 'component-accordion.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}

{%- assign first_3d_model = product.media | where: "media_type", "model" | first -%}
{%- if first_3d_model -%}
  {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
  <link id="ModelViewerStyle" rel="stylesheet" href="https://cdn.shopify.com/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">
  <link id="ModelViewerOverride" rel="stylesheet" href="{{ 'component-model-viewer-ui.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

<script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>

{%- if product.media.size > 1 -%}
  <script src="{{ 'product-media-gallery.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if first_3d_model -%}
  <script type="application/json" id="ProductJSON-{{ product.id }}">
    {{ product.media | where: 'media_type', 'model' | json }}
  </script>
  <script src="{{ 'product-model.js' | asset_url }}" defer></script>
{%- endif -%}

<div class="section-product">
  <div class="container">
    <div class="product">
      <div class="product__media-wrapper">
        {% render 'product-media-gallery', variant_images: variant_images %}
      </div>
      
      <div class="product__info-wrapper">
        <div class="product__info">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when '@app' -%}
                {% render block %}
              {%- when 'text' -%}
                <p class="product__text" {{ block.shopify_attributes }}>
                  {{ block.settings.text }}
                </p>
              {%- when 'title' -%}
                <div class="product__title" {{ block.shopify_attributes }}>
                  <h1>{{ product.title | escape }}</h1>
                </div>
              {%- when 'price' -%}
                <div class="product__price no-js-hidden" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                  {%- render 'price', product: product, use_variant: true, show_badges: true, price_class: 'price--large' -%}
                </div>
              {%- when 'variant_picker' -%}
                <div class="product__variant-picker" {{ block.shopify_attributes }}>
                  {% render 'product-variant-picker', product: product, block: block, product_form_id: product_form_id %}
                </div>
              {%- when 'quantity_selector' -%}
                <div class="product__quantity" {{ block.shopify_attributes }}>
                  {% render 'product-quantity-selector', product: product, block: block, product_form_id: product_form_id %}
                </div>
              {%- when 'buy_buttons' -%}
                <div class="product__buttons" {{ block.shopify_attributes }}>
                  {% render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true %}
                </div>
              {%- when 'description' -%}
                {%- if product.description != blank -%}
                  <div class="product__description rte" {{ block.shopify_attributes }}>
                    {{ product.description }}
                  </div>
                {%- endif -%}
              {%- when 'share' -%}
                <div class="product__share" {{ block.shopify_attributes }}>
                  {% render 'share-button', block: block %}
                </div>
              {%- when 'custom_liquid' -%}
                {{ block.settings.custom_liquid }}
              {%- when 'collapsible_tab' -%}
                <div class="product__accordion" {{ block.shopify_attributes }}>
                  {% render 'accordion', summary: block.settings.heading, content: block.settings.content, icon: block.settings.icon %}
                </div>
              {%- when 'popup' -%}
                <div class="product__popup" {{ block.shopify_attributes }}>
                  <button
                    id="ProductPopup-{{ block.id }}"
                    class="product__popup-button link"
                    type="button"
                    aria-haspopup="dialog"
                  >
                    {{ block.settings.text | default: block.settings.page.title }}
                  </button>
                  <div
                    id="ProductPopup-Modal-{{ block.id }}"
                    class="product__popup-modal modal"
                    role="dialog"
                    aria-labelledby="ProductPopup-{{ block.id }}"
                    aria-hidden="true"
                  >
                    <div class="product__popup-modal-content">
                      <h2 id="ProductPopup-title-{{ block.id }}">
                        {{ block.settings.page.title | default: block.settings.text }}
                      </h2>
                      {{ block.settings.page.content }}
                      <button
                        id="ModalClose-{{ block.id }}"
                        type="button"
                        class="product__popup-modal-toggle"
                        aria-label="{{ 'accessibility.close' | t }}"
                      >
                        {% render 'icon-close' %}
                      </button>
                    </div>
                  </div>
                </div>
              {%- when 'rating' -%}
                {%- if product.metafields.reviews.rating.value != blank -%}
                  <div class="product__rating" {{ block.shopify_attributes }}>
                    {% render 'product-rating', value: product.metafields.reviews.rating.value, scale_max: product.metafields.reviews.rating.value.scale_max %}
                  </div>
                {%- endif -%}
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
    </div>

    {%- if section.settings.enable_product_tabs -%}
      <div class="product__tabs">
        {% render 'product-tabs', product: product %}
      </div>
    {%- endif -%}

    {%- if section.settings.show_trust_badges -%}
      <div class="product__trust-section">
        {% render 'product-trust-badges' %}
      </div>
    {%- endif -%}
  </div>
</div>

<script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>

{%- liquid
  if product.selected_or_first_available_variant
    assign target = product.selected_or_first_available_variant
  else
    assign target = product
  endif
  assign product_form_id = 'product-form-' | append: section.id
-%}

{% schema %}
{
  "name": "t:sections.product.name",
  "tag": "section",
  "class": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.product.blocks.text.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Text block",
          "label": "t:sections.product.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.product.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.product.blocks.title.name",
      "limit": 1
    },
    {
      "type": "price",
      "name": "t:sections.product.blocks.price.name",
      "limit": 1
    },
    {
      "type": "variant_picker",
      "name": "t:sections.product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.product.blocks.variant_picker.settings.picker_type.label"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.product.blocks.quantity_selector.name",
      "limit": 1
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": true,
          "label": "t:sections.product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": true,
          "label": "t:sections.product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
          "info": "t:sections.product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.product.blocks.description.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.product.blocks.share.settings.text.label",
          "default": "Share"
        },
        {
          "type": "paragraph",
          "content": "t:sections.product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.product.blocks.share.settings.title_info.content"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.product.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.product.blocks.custom_liquid.settings.custom_liquid.label",
          "info": "t:sections.product.blocks.custom_liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "info": "t:sections.product.blocks.collapsible_tab.settings.heading.info",
          "label": "t:sections.product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "none",
          "label": "t:sections.product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.product.blocks.collapsible_tab.settings.page.label"
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.product.blocks.popup.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Pop-up link text",
          "label": "t:sections.product.blocks.popup.settings.link_label.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.product.blocks.popup.settings.page.label"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.product.blocks.rating.settings.paragraph.content"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "default": true,
      "label": "t:sections.product.settings.enable_sticky_info.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.product.settings.media_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.product.settings.media_size.label",
      "info": "t:sections.product.settings.media_size.info"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.product.settings.media_fit.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "FEG+ Specific Settings"
    },
    {
      "type": "checkbox",
      "id": "enable_product_tabs",
      "default": true,
      "label": "Enable Product Information Tabs"
    },
    {
      "type": "checkbox",
      "id": "show_trust_badges",
      "default": true,
      "label": "Show Trust Badges on Product Page"
    }
  ]
}
{% endschema %}

{{ 'section-product.css' | asset_url | stylesheet_tag }}
{{ 'component-accordion.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}

{%- assign first_3d_model = product.media | where: "media_type", "model" | first -%}
{%- if first_3d_model -%}
  {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
  <link id="ModelViewerStyle" rel="stylesheet" href="https://cdn.shopify.com/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">
  <link id="ModelViewerOverride" rel="stylesheet" href="{{ 'component-model-viewer-ui.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

<script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>

{%- if product.media.size > 1 -%}
  <script src="{{ 'product-media-gallery.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if first_3d_model -%}
  <script type="application/json" id="ProductJSON-{{ product.id }}">
    {{ product.media | where: 'media_type', 'model' | json }}
  </script>
  <script src="{{ 'product-model.js' | asset_url }}" defer></script>
{%- endif -%}

<div class="section-product">
  <div class="container">
    <div class="product">
      <div class="product__media-wrapper">
        {% render 'product-media-gallery', variant_images: variant_images %}
      </div>
      
      <div class="product__info-wrapper">
        <div class="product__info">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when '@app' -%}
                {% render block %}
              {%- when 'text' -%}
                <p class="product__text" {{ block.shopify_attributes }}>
                  {{ block.settings.text }}
                </p>
              {%- when 'title' -%}
                <div class="product__title" {{ block.shopify_attributes }}>
                  <h1>{{ product.title | escape }}</h1>
                </div>
              {%- when 'price' -%}
                <div class="product__price no-js-hidden" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                  {%- render 'price', product: product, use_variant: true, show_badges: true, price_class: 'price--large' -%}
                </div>
              {%- when 'variant_picker' -%}
                <div class="product__variant-picker" {{ block.shopify_attributes }}>
                  {% render 'product-variant-picker', product: product, block: block, product_form_id: product_form_id %}
                </div>
              {%- when 'quantity_selector' -%}
                <div class="product__quantity" {{ block.shopify_attributes }}>
                  {% render 'product-quantity-selector', product: product, block: block, product_form_id: product_form_id %}
                </div>
              {%- when 'buy_buttons' -%}
                <div class="product__buttons" {{ block.shopify_attributes }}>
                  {% render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true %}
                </div>
              {%- when 'description' -%}
                {%- if product.description != blank -%}
                  <div class="product__description rte" {{ block.shopify_attributes }}>
                    {{ product.description }}
                  </div>
                {%- endif -%}
              {%- when 'share' -%}
                <div class="product__share" {{ block.shopify_attributes }}>
                  {% render 'share-button', block: block %}
                </div>
              {%- when 'custom_liquid' -%}
                {{ block.settings.custom_liquid }}
              {%- when 'collapsible_tab' -%}
                <div class="product__accordion" {{ block.shopify_attributes }}>
                  {% render 'accordion', summary: block.settings.heading, content: block.settings.content, icon: block.settings.icon %}
                </div>
              {%- when 'popup' -%}
                <div class="product__popup" {{ block.shopify_attributes }}>
                  <button
                    id="ProductPopup-{{ block.id }}"
                    class="product__popup-button link"
                    type="button"
                    aria-haspopup="dialog"
                  >
                    {{ block.settings.text | default: block.settings.page.title }}
                  </button>
                  <div
                    id="ProductPopup-Modal-{{ block.id }}"
                    class="product__popup-modal modal"
                    role="dialog"
                    aria-labelledby="ProductPopup-{{ block.id }}"
                    aria-hidden="true"
                  >
                    <div class="product__popup-modal-content">
                      <h2 id="ProductPopup-title-{{ block.id }}">
                        {{ block.settings.page.title | default: block.settings.text }}
                      </h2>
                      {{ block.settings.page.content }}
                      <button
                        id="ModalClose-{{ block.id }}"
                        type="button"
                        class="product__popup-modal-toggle"
                        aria-label="{{ 'accessibility.close' | t }}"
                      >
                        {% render 'icon-close' %}
                      </button>
                    </div>
                  </div>
                </div>
              {%- when 'rating' -%}
                {%- if product.metafields.reviews.rating.value != blank -%}
                  <div class="product__rating" {{ block.shopify_attributes }}>
                    {% render 'product-rating', value: product.metafields.reviews.rating.value, scale_max: product.metafields.reviews.rating.value.scale_max %}
                  </div>
                {%- endif -%}
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
    </div>

    {%- if section.settings.enable_product_tabs -%}
      <div class="product__tabs">
        {% render 'product-tabs', product: product %}
      </div>
    {%- endif -%}

    {%- if section.settings.show_trust_badges -%}
      <div class="product__trust-section">
        {% render 'product-trust-badges' %}
      </div>
    {%- endif -%}
  </div>
</div>

<script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>

{%- liquid
  if product.selected_or_first_available_variant
    assign target = product.selected_or_first_available_variant
  else
    assign target = product
  endif
  assign product_form_id = 'product-form-' | append: section.id
-%}



{%- style -%}
  .banner {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 600px;
    overflow: hidden;
  }

  .banner--large {
    min-height: 700px;
  }

  .banner--medium {
    min-height: 500px;
  }

  .banner--small {
    min-height: 400px;
  }

  @media screen and (max-width: 749px) {
    .banner {
      min-height: 400px;
    }
    
    .banner--large {
      min-height: 500px;
    }
    
    .banner--medium {
      min-height: 400px;
    }
    
    .banner--small {
      min-height: 300px;
    }
  }

  .banner__media {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
  }

  .banner__media img,
  .banner__media video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .banner__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, {{ section.settings.image_overlay_opacity | divided_by: 100.0 }});
    z-index: -1;
  }

  .banner__content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: 60px 0;
  }

  .banner__box {
    max-width: 600px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    backdrop-filter: blur(10px);
  }

  @media screen and (max-width: 749px) {
    .banner__box {
      padding: 30px 20px;
      margin: 0 15px;
    }
  }

  .banner__heading {
    margin-bottom: 20px;
    color: var(--color-text);
    font-weight: 700;
    line-height: 1.2;
  }

  .banner__text {
    margin-bottom: 30px;
    color: var(--color-text-light);
    font-size: 1.125rem;
    line-height: 1.6;
  }

  .banner__buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  @media screen and (max-width: 749px) {
    .banner__buttons {
      flex-direction: column;
    }
    
    .banner__buttons .btn {
      width: 100%;
    }
  }

  .banner--desktop-left .banner__content {
    justify-content: flex-start;
    text-align: left;
  }

  .banner--desktop-center .banner__content {
    justify-content: center;
    text-align: center;
  }

  .banner--desktop-right .banner__content {
    justify-content: flex-end;
    text-align: right;
  }

  .banner--mobile-left .banner__content {
    text-align: left;
  }

  .banner--mobile-center .banner__content {
    text-align: center;
  }

  .banner--mobile-right .banner__content {
    text-align: right;
  }

  @media screen and (max-width: 749px) {
    .banner--mobile-left .banner__content {
      text-align: left;
    }

    .banner--mobile-center .banner__content {
      text-align: center;
    }

    .banner--mobile-right .banner__content {
      text-align: right;
    }
  }

  .banner__content-inner {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .banner__content--center .banner__content-inner {
    align-items: center;
  }
{%- endstyle -%}

<div class="banner banner--{{ section.settings.image_height }} banner--desktop-{{ section.settings.desktop_content_position | split: '-' | first }} banner--mobile-{{ section.settings.mobile_content_alignment }} color-{{ section.settings.color_scheme }} gradient">
  {%- if section.settings.image != blank -%}
    <div class="banner__media">
      {%- liquid
        assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
        if section.settings.image_behavior == 'ambient'
          assign sizes = '120vw'
        else
          assign sizes = '100vw'
        endif
      -%}
      {{ section.settings.image | image_url: width: 3840 | image_tag:
        loading: 'lazy',
        width: section.settings.image.width,
        height: image_height,
        sizes: sizes,
        widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840',
        alt: section.settings.image.alt | escape
      }}
    </div>
    <div class="banner__overlay"></div>
  {%- endif -%}

  <div class="banner__content container">
    <div class="banner__content-inner{% if section.settings.desktop_content_position contains 'center' %} banner__content--center{% endif %}">
      {%- if section.settings.show_text_box -%}
        <div class="banner__box">
      {%- endif -%}
      
      {%- if section.settings.heading != blank -%}
        <h1 class="banner__heading {{ section.settings.heading_size }}">
          {{ section.settings.heading | escape }}
        </h1>
      {%- endif -%}
      
      {%- if section.settings.subheading != blank -%}
        <div class="banner__text">
          {{ section.settings.subheading | escape }}
        </div>
      {%- endif -%}
      
      {%- if section.settings.button_label != blank or section.settings.second_button_label != blank -%}
        <div class="banner__buttons">
          {%- if section.settings.button_label != blank -%}
            <a class="btn {% if section.settings.button_style_secondary %}btn--secondary{% else %}btn--primary{% endif %} btn--large" href="{{ section.settings.button_link }}">
              {{ section.settings.button_label | escape }}
            </a>
          {%- endif -%}
          {%- if section.settings.second_button_label != blank -%}
            <a class="btn {% if section.settings.second_button_style_secondary %}btn--secondary{% else %}btn--primary{% endif %} btn--large" href="{{ section.settings.second_button_link }}">
              {{ section.settings.second_button_label | escape }}
            </a>
          {%- endif -%}
        </div>
      {%- endif -%}
      
      {%- if section.settings.show_text_box -%}
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Hero Banner",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 80,
      "step": 5,
      "unit": "%",
      "label": "Image overlay opacity",
      "default": 0
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium",
      "label": "Image height",
      "info": "Learn more about [image dimensions](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-dimension-units)"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top-left",
          "label": "Top left"
        },
        {
          "value": "top-center",
          "label": "Top center"
        },
        {
          "value": "top-right",
          "label": "Top right"
        },
        {
          "value": "middle-left",
          "label": "Middle left"
        },
        {
          "value": "middle-center",
          "label": "Middle center"
        },
        {
          "value": "middle-right",
          "label": "Middle right"
        },
        {
          "value": "bottom-left",
          "label": "Bottom left"
        },
        {
          "value": "bottom-center",
          "label": "Bottom center"
        },
        {
          "value": "bottom-right",
          "label": "Bottom right"
        }
      ],
      "default": "middle-center",
      "label": "Desktop content position"
    },
    {
      "type": "checkbox",
      "id": "show_text_box",
      "default": true,
      "label": "Show text box"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Desktop content alignment"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Mobile content alignment"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Transform Your Hair with FEG+ Hair Spray Serum",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "text",
      "id": "subheading",
      "default": "Clinically proven dual-action formula combining Minoxidil with 22% Seaweed Extract for visible hair growth results in just 30 days.",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Shop Now",
      "label": "First button label",
      "info": "Leave the label blank to hide the button."
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "First button link"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "Use outline button style"
    },
    {
      "type": "text",
      "id": "second_button_label",
      "default": "Learn More",
      "label": "Second button label",
      "info": "Leave the label blank to hide the button."
    },
    {
      "type": "url",
      "id": "second_button_link",
      "label": "Second button link"
    },
    {
      "type": "checkbox",
      "id": "second_button_style_secondary",
      "default": false,
      "label": "Use outline button style for second button"
    }
  ],
  "presets": [
    {
      "name": "Hero Banner"
    }
  ]
}
{% endschema %}

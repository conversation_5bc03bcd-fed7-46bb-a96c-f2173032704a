{"general": {"404": {"title": "404 Page Not Found", "subtext_html": "The page you were looking for does not exist.", "link": "Continue shopping", "back_to_home": "Back to Home"}, "accessibility": {"skip_to_text": "Skip to content", "close": "Close", "unit_price_separator": "per", "vendor": "Vendor:", "error": "Error", "refresh_page": "Choosing a selection results in a full page refresh.", "link_messages": {"new_window": "Opens in a new window.", "external": "Opens external website."}}, "meta": {"tags": "Tagged \"{{ tags }}\"", "page": "Page {{ page }}"}, "breadcrumbs": {"home": "Home", "create_account": "Create account", "account": "Account", "addresses": "Addresses"}, "pagination": {"label": "Pagination", "page": "Page {{ number }}", "current_page": "Page {{ number }} (current)", "previous": "Previous page", "next": "Next page"}, "password_page": {"login_form_heading": "Enter store using password:", "login_password_button": "Enter using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_error": "Wrong password!", "login_form_submit": "Enter", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link underlined-link\">Log in here</a>", "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Share on Facebook", "share_on_twitter": "Tweet on Twitter", "share_on_pinterest": "Pin on Pinterest"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Continue shopping", "search": {"search": "Search", "products": "Products", "suggestions": "Suggestions", "pages": "Pages", "collections": "Collections", "results_with_count": {"one": "{{ count }} result for \"{{ terms }}\"", "other": "{{ count }} results for \"{{ terms }}\""}, "results_with_count_and_term": {"one": "{{ count }} result found for \"{{ terms }}\"", "other": "{{ count }} results found for \"{{ terms }}\""}, "no_results": "No results could be found. Please try again with a different query.", "placeholder": "Search", "submit": "Submit"}, "cart": {"view": "View my cart ({{ count }})", "item_added": "Item added to your cart"}}, "sections": {"header": {"announcement": "Announcement", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} item", "other": "{{ count }} items"}}, "cart": {"title": "Your cart", "caption": "Cart items", "remove_title": "Remove {{ title }}", "remove": "Remove", "subtotal": "Subtotal", "new_subtotal": "New subtotal", "note": "Order special instructions", "checkout": "Check out", "empty": "Your cart is empty", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add {{ quantity }} of this item to your cart.", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "taxes_and_shipping_at_checkout": "Taxes and shipping calculated at checkout", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity"}, "update": "Update", "login": {"title": "Have an account?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."}}, "footer": {"payment": "Payment methods"}}, "collections": {"general": {"view_all": "View all", "clear_all": "Clear all", "no_matches": "Sorry, there are no products in this collection", "items_with_count": {"one": "{{ count }} product", "other": "{{ count }} products"}}}, "products": {"product": {"add_to_cart": "Add to cart", "description": "Description", "on_sale": "Sale", "product_variants": "Product variants", "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}", "increase": "Increase quantity for {{ product }}", "decrease": "Decrease quantity for {{ product }}"}, "price": {"from_price_html": "From {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "share": "Share this product", "sold_out": "Sold out", "unavailable": "Unavailable", "vendor": "<PERSON><PERSON><PERSON>", "video_exit_message": "{{ title }} opens full screen video in same window.", "xr_button": "View in your space", "xr_button_label": "View in your space, loads item in augmented reality window", "pickup_availability": {"view_store_info": "View store information", "check_other_stores": "Check availability at other stores", "pick_up_available": "Pickup available", "pick_up_available_at_html": "Pickup available at <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Pickup currently unavailable at <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Couldn't load pickup availability", "refresh": "Refresh"}, "media": {"open_media": "Open media {{ index }} in gallery view", "play_model": "Play 3D Viewer", "play_video": "Play video", "gallery_viewer": "Gallery Viewer", "load_image": "Load image {{ index }} in gallery view", "load_model": "Load 3D Model {{ index }} in gallery view", "load_video": "Play video {{ index }} in gallery view", "image_available": "Image {{ index }} is now available in gallery view"}, "view_full_details": "View full details", "include_taxes": "Tax included.", "shipping_policy_html": "<a href=\"{{ link }}\">Shipping</a> calculated at checkout."}, "modal": {"label": "Media gallery"}, "facets": {"apply": "Apply", "clear": "Clear", "clear_all": "Clear all", "from": "From", "filter_and_sort": "Filter and sort", "filter_by_label": "Filter:", "filter_button": "Filter", "filters_selected": {"one": "{{ count }} selected", "other": "{{ count }} selected"}, "max_price": "The highest price is {{ price }}", "product_count": {"one": "{{ product_count }} of {{ count }} product", "other": "{{ product_count }} of {{ count }} products"}, "product_count_simple": {"one": "{{ count }} product", "other": "{{ count }} products"}, "reset": "Reset", "sort_button": "Sort", "sort_by_label": "Sort by:", "to": "To", "clear_filter": "Clear filter"}}, "templates": {"search": {"no_results": "No results found for \"{{ terms }}\". Check the spelling or use a different word or phrase.", "results_with_count": {"one": "{{ count }} result", "other": "{{ count }} results"}, "title": "Search results", "page": "Page", "products": "Products", "search_for": "Search for \"{{ terms }}\"", "results_with_count_and_term": {"one": "{{ count }} result found for \"{{ terms }}\"", "other": "{{ count }} results found for \"{{ terms }}\""}}, "cart": {"cart": "<PERSON><PERSON>"}, "contact": {"form": {"name": "Name", "email": "Email", "phone": "Phone number", "comment": "Comment", "send": "Send", "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.", "error_heading": "Please adjust the following:"}}, "404": {"title": "Page not found", "subtext": "404"}}, "customer": {"account": {"title": "Account", "details": "Account details", "view_addresses": "View addresses", "return": "Return to Account details"}, "account_fallback": "Account", "activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "addresses": {"title": "Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/Region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "log_in": "Log in", "log_out": "Log out", "login_page": {"cancel": "Cancel", "create_account": "Create account", "email": "Email", "forgot_password": "Forgot your password?", "guest_continue": "Continue", "guest_title": "Continue as a guest", "password": "Password", "title": "<PERSON><PERSON>", "sign_in": "Sign in", "submit": "Submit"}, "orders": {"title": "Order history", "order_number": "Order", "order_number_link": "Order number {{ number }}", "date": "Date", "payment_status": "Payment status", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}, "recover_password": {"title": "Reset your password", "subtext": "We will send you an email to reset your password", "email": "Email", "submit": "Submit", "cancel": "Cancel", "success": "We've sent you an email with a link to update your password."}, "register": {"title": "Create account", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create"}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password for {{ email }}", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset password"}}}
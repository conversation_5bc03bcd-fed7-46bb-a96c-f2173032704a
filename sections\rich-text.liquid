{% comment %}
  Rich Text Section
  Displays rich text content with customizable styling
{% endcomment %}

<div class="rich-text color-{{ section.settings.color_scheme }} gradient">
  <div class="rich-text__wrapper section-{{ section.id }}-padding">
    <div class="page-width">
      <div class="rich-text__blocks {{ section.settings.desktop_content_position }}">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'heading' -%}
              <h2 class="rich-text__heading rte inline-richtext {{ block.settings.heading_size }}" {{ block.shopify_attributes }}>
                {{ block.settings.heading }}
              </h2>
            {%- when 'caption' -%}
              <p class="rich-text__caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }}" {{ block.shopify_attributes }}>
                {{ block.settings.caption | escape }}
              </p>
            {%- when 'text' -%}
              <div class="rich-text__text rte" {{ block.shopify_attributes }}>
                {{ block.settings.text }}
              </div>
            {%- when 'button' -%}
              <div class="rich-text__buttons" {{ block.shopify_attributes }}>
                <a class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                   {% if block.settings.button_link == blank %}role="button" aria-disabled="true"{% else %}href="{{ block.settings.button_link }}"{% endif %}>
                  {{ block.settings.button_label | escape }}
                </a>
              </div>
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>
</div>

{%- if section.settings.heading != blank or section.settings.text != blank or section.settings.button_label != blank -%}
  <div class="rich-text color-{{ section.settings.color_scheme }} gradient">
    <div class="rich-text__wrapper section-{{ section.id }}-padding">
      <div class="page-width">
        <div class="rich-text__blocks {{ section.settings.desktop_content_position }}">
          {%- if section.settings.heading != blank -%}
            <h2 class="rich-text__heading rte inline-richtext {{ section.settings.heading_size }}">
              {{ section.settings.heading }}
            </h2>
          {%- endif -%}
          {%- if section.settings.text != blank -%}
            <div class="rich-text__text rte">
              {{ section.settings.text }}
            </div>
          {%- endif -%}
          {%- if section.settings.button_label != blank -%}
            <div class="rich-text__buttons">
              <a class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                 {% if section.settings.button_link == blank %}role="button" aria-disabled="true"{% else %}href="{{ section.settings.button_link }}"{% endif %}>
                {{ section.settings.button_label | escape }}
              </a>
            </div>
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
{%- endif -%}

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .rich-text__wrapper {
    display: flex;
    justify-content: center;
  }

  .rich-text__blocks {
    text-align: {{ section.settings.content_alignment }};
    max-width: 100%;
  }

  .rich-text__blocks.left {
    text-align: left;
  }

  .rich-text__blocks.center {
    text-align: center;
  }

  .rich-text__blocks.right {
    text-align: right;
  }

  .rich-text__heading {
    margin-bottom: 1rem;
  }

  .rich-text__text {
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .rich-text__buttons {
    margin-top: 2rem;
  }

  .button {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .button--primary {
    background-color: #007bff;
    color: white;
  }

  .button--primary:hover {
    background-color: #0056b3;
  }

  .button--secondary {
    background-color: transparent;
    color: #007bff;
    border-color: #007bff;
  }

  .button--secondary:hover {
    background-color: #007bff;
    color: white;
  }
</style>

{% schema %}
{
  "name": "Rich text",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "Rich text",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "text",
      "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "Button label",
      "info": "Leave the label blank to hide the button."
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "Use outline button style"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Desktop content position",
      "info": "Position is automatically optimized for mobile."
    },
    {
      "type": "select",
      "id": "content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Content alignment"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "background-1",
      "label": "Color scheme"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "Make section full width"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 52
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "limit": 3,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Rich text",
          "label": "Heading"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "Small"
            },
            {
              "value": "h1",
              "label": "Medium"
            },
            {
              "value": "h0",
              "label": "Large"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "caption",
      "name": "Caption",
      "limit": 3,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "Subtitle"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "Caption with letter spacing"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "Text style"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium",
          "label": "Text size"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "limit": 3,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>",
          "label": "Text"
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "limit": 2,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "Button label",
          "info": "Leave the label blank to hide the button."
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "Use outline button style"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Rich text",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}

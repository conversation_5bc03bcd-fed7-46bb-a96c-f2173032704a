/* FEG+ Hair Spray Serum Theme - Base Styles */

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-body-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading-family);
  font-weight: var(--font-heading-weight);
  line-height: 1.2;
  margin: 0 0 1rem 0;
  color: var(--color-text);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

@media screen and (max-width: 749px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
}

p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--color-secondary);
}

/* Layout Components */
.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 20px;
}

@media screen and (max-width: 749px) {
  .container {
    padding: 0 15px;
  }
}

.section {
  padding: 60px 0;
}

@media screen and (max-width: 749px) {
  .section {
    padding: 40px 0;
  }
}

.section--small {
  padding: 40px 0;
}

@media screen and (max-width: 749px) {
  .section--small {
    padding: 30px 0;
  }
}

/* Grid System */
.grid {
  display: grid;
  gap: 30px;
}

.grid--2-col {
  grid-template-columns: repeat(2, 1fr);
}

.grid--3-col {
  grid-template-columns: repeat(3, 1fr);
}

.grid--4-col {
  grid-template-columns: repeat(4, 1fr);
}

@media screen and (max-width: 990px) {
  .grid--3-col,
  .grid--4-col {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 749px) {
  .grid--2-col,
  .grid--3-col,
  .grid--4-col {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 15px 30px;
  font-family: var(--font-heading-family);
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1;
  min-height: 50px;
}

.btn--primary {
  background-color: var(--color-primary);
  color: white;
}

.btn--primary:hover {
  background-color: var(--color-secondary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(45, 90, 61, 0.3);
}

.btn--secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.btn--secondary:hover {
  background-color: var(--color-primary);
  color: white;
}

.btn--large {
  padding: 20px 40px;
  font-size: 1.125rem;
  min-height: 60px;
}

@media screen and (max-width: 749px) {
  .btn {
    padding: 12px 25px;
    font-size: 0.9rem;
    min-height: 45px;
  }
  
  .btn--large {
    padding: 15px 30px;
    font-size: 1rem;
    min-height: 50px;
  }
}

/* Cards */
.card {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.15);
}

@media screen and (max-width: 749px) {
  .card {
    padding: 20px;
  }
}

/* Trust Badges */
.trust-badges {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  margin: 30px 0;
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: rgba(45, 90, 61, 0.1);
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-primary);
}

.trust-badge svg {
  width: 20px;
  height: 20px;
  fill: var(--color-primary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  word-wrap: normal !important;
}

/* Skip to content link */
.skip-to-content-link {
  position: absolute;
  z-index: 9999;
  top: -40px;
  left: 6px;
  background-color: var(--color-primary);
  color: white;
  padding: 8px;
  border-radius: 4px;
  text-decoration: none;
  transition: top 0.3s;
}

.skip-to-content-link:focus {
  top: 6px;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-overlay__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-primary);
  border-top: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

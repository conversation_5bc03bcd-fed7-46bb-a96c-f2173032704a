{% comment %}
  Page Template
  For static pages like About, Contact, Privacy Policy, etc.
{% endcomment %}

<div class="template-page">
  <div class="page-width">
    <div class="page-header">
      <h1 class="page-title">{{ page.title }}</h1>
    </div>
    
    <div class="page-content">
      <div class="rte">
        {{ page.content }}
      </div>
    </div>
  </div>
</div>

<style>
.template-page {
  padding: 40px 0;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #333;
}

.page-content {
  max-width: 800px;
  margin: 0 auto;
}

.rte {
  line-height: 1.6;
  color: #555;
}

.rte h1, .rte h2, .rte h3, .rte h4, .rte h5, .rte h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #333;
}

.rte h1 { font-size: 2rem; }
.rte h2 { font-size: 1.75rem; }
.rte h3 { font-size: 1.5rem; }
.rte h4 { font-size: 1.25rem; }

.rte p {
  margin-bottom: 1rem;
}

.rte ul, .rte ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.rte li {
  margin-bottom: 0.5rem;
}

.rte a {
  color: #007bff;
  text-decoration: none;
}

.rte a:hover {
  text-decoration: underline;
}

.rte img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

.rte blockquote {
  border-left: 4px solid #007bff;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #666;
}

.rte table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.rte th, .rte td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.rte th {
  background-color: #f8f9fa;
  font-weight: 600;
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .template-page {
    padding: 20px 0;
  }
  
  .page-content {
    padding: 0 20px;
  }
}
</style>

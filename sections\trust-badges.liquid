{%- style -%}
  .trust-section {
    padding: {{ section.settings.padding_top }}px 0 {{ section.settings.padding_bottom }}px;
    background: var(--color-background);
  }

  .trust-section__heading {
    text-align: center;
    margin-bottom: 40px;
    color: var(--color-text);
  }

  .trust-badges {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
  }

  @media screen and (max-width: 749px) {
    .trust-badges {
      gap: 20px;
      justify-content: space-around;
    }
  }

  .trust-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-width: 180px;
    border: 2px solid transparent;
  }

  .trust-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    border-color: var(--color-primary);
  }

  @media screen and (max-width: 749px) {
    .trust-badge {
      min-width: 140px;
      padding: 15px;
    }
  }

  .trust-badge__icon {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
    fill: var(--color-primary);
    stroke: var(--color-primary);
  }

  @media screen and (max-width: 749px) {
    .trust-badge__icon {
      width: 40px;
      height: 40px;
      margin-bottom: 10px;
    }
  }

  .trust-badge__title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 8px;
    line-height: 1.3;
  }

  .trust-badge__subtitle {
    font-size: 0.85rem;
    color: var(--color-text-light);
    line-height: 1.4;
  }

  @media screen and (max-width: 749px) {
    .trust-badge__title {
      font-size: 0.9rem;
    }
    
    .trust-badge__subtitle {
      font-size: 0.8rem;
    }
  }
{%- endstyle -%}

<div class="trust-section section color-{{ section.settings.color_scheme }} gradient">
  <div class="container">
    {%- if section.settings.title != blank -%}
      <h2 class="trust-section__heading h2">
        {{ section.settings.title | escape }}
      </h2>
    {%- endif -%}

    <div class="trust-badges">
      {%- if section.settings.show_made_in_usa -%}
        <div class="trust-badge">
          <svg class="trust-badge__icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L22 9L17 14L18 22L12 19L6 22L7 14L2 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div class="trust-badge__title">Made in USA</div>
          <div class="trust-badge__subtitle">FDA-registered facilities</div>
        </div>
      {%- endif -%}

      {%- if section.settings.show_money_back_guarantee -%}
        <div class="trust-badge">
          <svg class="trust-badge__icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div class="trust-badge__title">30-Day Guarantee</div>
          <div class="trust-badge__subtitle">Money back promise</div>
        </div>
      {%- endif -%}

      {%- if section.settings.show_secure_checkout -%}
        <div class="trust-badge">
          <svg class="trust-badge__icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22S8 18 8 13V6L12 4L16 6V13C16 18 12 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div class="trust-badge__title">Secure Checkout</div>
          <div class="trust-badge__subtitle">SSL encrypted</div>
        </div>
      {%- endif -%}

      {%- if section.settings.show_fast_shipping -%}
        <div class="trust-badge">
          <svg class="trust-badge__icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V8L16 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M16 3V8H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M13 13L16 10L13 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 13H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div class="trust-badge__title">Fast Shipping</div>
          <div class="trust-badge__subtitle">Free on orders $50+</div>
        </div>
      {%- endif -%}

      {%- if section.settings.show_customer_support -%}
        <div class="trust-badge">
          <svg class="trust-badge__icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div class="trust-badge__title">Expert Support</div>
          <div class="trust-badge__subtitle">Here to help you</div>
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Trust Badges",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "Your Trust is Our Priority",
      "label": "Heading"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "background-2"
    },
    {
      "type": "header",
      "content": "Trust Elements"
    },
    {
      "type": "checkbox",
      "id": "show_made_in_usa",
      "default": true,
      "label": "Show 'Made in USA' badge"
    },
    {
      "type": "checkbox",
      "id": "show_money_back_guarantee",
      "default": true,
      "label": "Show money back guarantee"
    },
    {
      "type": "checkbox",
      "id": "show_secure_checkout",
      "default": true,
      "label": "Show secure checkout badge"
    },
    {
      "type": "checkbox",
      "id": "show_fast_shipping",
      "default": true,
      "label": "Show fast shipping badge"
    },
    {
      "type": "checkbox",
      "id": "show_customer_support",
      "default": false,
      "label": "Show customer support badge"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 40
    }
  ],
  "presets": [
    {
      "name": "Trust Badges"
    }
  ]
}
{% endschema %}

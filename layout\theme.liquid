<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_primary }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- unless settings.font_heading.system? and settings.font_body.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}

    <script src="{{ 'global.js' | asset_url }}" defer="defer"></script>
    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.font_body | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.font_body | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}

    {% style %}
      {{ settings.font_heading | font_face: font_display: 'swap' }}
      {{ settings.font_body | font_face: font_display: 'swap' }}
      {{ body_font_bold | font_face: font_display: 'swap' }}
      {{ body_font_italic | font_face: font_display: 'swap' }}
      {{ body_font_bold_italic | font_face: font_display: 'swap' }}

      :root {
        --font-heading-family: {{ settings.font_heading.family }}, {{ settings.font_heading.fallback_families }};
        --font-heading-style: {{ settings.font_heading.style }};
        --font-heading-weight: {{ settings.font_heading.weight }};

        --font-body-family: {{ settings.font_body.family }}, {{ settings.font_body.fallback_families }};
        --font-body-style: {{ settings.font_body.style }};
        --font-body-weight: {{ settings.font_body.weight }};

        --color-primary: {{ settings.color_primary }};
        --color-secondary: {{ settings.color_secondary }};
        --color-accent: {{ settings.color_accent }};
        --color-background: {{ settings.color_background }};
        --color-text: {{ settings.color_text }};
        --color-text-light: {{ settings.color_text_light }};

        --font-size-base: {{ settings.font_size_base }}px;
        --container-width: {{ settings.container_width }}px;
      }

      *,
      *::before,
      *::after {
        box-sizing: inherit;
      }

      html {
        box-sizing: border-box;
        font-size: var(--font-size-base);
        height: 100%;
      }

      body {
        display: grid;
        grid-template-rows: auto auto 1fr auto;
        grid-template-columns: 100%;
        min-height: 100%;
        margin: 0;
        font-size: 1rem;
        letter-spacing: 0.06rem;
        line-height: calc(1 + 0.8 / var(--font-body-scale));
        font-family: var(--font-body-family);
        font-style: var(--font-body-style);
        font-weight: var(--font-body-weight);
        color: var(--color-text);
        background-color: var(--color-background);
      }

      .container {
        max-width: var(--container-width);
        margin: 0 auto;
        padding: 0 20px;
      }

      @media screen and (max-width: 749px) {
        .container {
          padding: 0 15px;
        }
      }
    {% endstyle %}

    {{ 'base.css' | asset_url | stylesheet_tag }}
    {{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}

    {%- unless settings.font_body.system? -%}
      <link rel="preload" as="font" href="{{ settings.font_body | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.font_heading.system? -%}
      <link rel="preload" as="font" href="{{ settings.font_heading | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    <script>document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    if (Shopify.designMode) {
      document.documentElement.classList.add('shopify-design-mode');
    }
    </script>
  </head>

  <body class="gradient">
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">
      {{ "accessibility.skip_to_text" | t }}
    </a>

    {%- if settings.cart_type == 'drawer' -%}
      {%- render 'cart-drawer' -%}
    {%- endif -%}

    {% section 'announcement-bar' %}
    {% section 'header' %}
    
    <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
      {{ content_for_layout }}
    </main>

    {% section 'footer' %}

    <ul hidden>
      <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
      <li id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</li>
    </ul>

    <script>
      window.shopUrl = '{{ request.origin }}';
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}',
        cart_url: '{{ routes.cart_url }}',
        predictive_search_url: '{{ routes.predictive_search_url }}'
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`
      }

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
        unavailable_with_option: `{{ 'products.product.value_unavailable' | t: option_value: '[value]' }}`,
      }

      window.accessibilityStrings = {
        imageAvailable: `{{ 'products.product.media.image_available' | t: index: '[index]' }}`,
        shareSuccess: `{{ 'general.share.success_message' | t }}`,
        pauseSlideshow: `{{ 'sections.slideshow.pause_slideshow' | t }}`,
        playSlideshow: `{{ 'sections.slideshow.play_slideshow' | t }}`,
      }
    </script>

    {%- if settings.predictive_search_enabled -%}
      <script src="{{ 'predictive-search.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}
  </body>
</html>

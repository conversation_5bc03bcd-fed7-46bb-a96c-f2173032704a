{% comment %}
  Search Template
  Displays search results
{% endcomment %}

<div class="template-search">
  <div class="page-width">
    <div class="search-header">
      <h1 class="search-title">
        {% if search.performed %}
          {{ 'templates.search.results_with_count' | t: terms: search.terms, count: search.results_count | default: 'Search Results' }}
        {% else %}
          {{ 'general.search.search' | t | default: 'Search' }}
        {% endif %}
      </h1>
      
      <form action="{{ routes.search_url }}" method="get" class="search-form">
        <div class="search-input-wrapper">
          <input type="search" 
                 name="q" 
                 value="{{ search.terms | escape }}" 
                 placeholder="{{ 'general.search.placeholder' | t | default: 'Search products...' }}"
                 class="search-input"
                 aria-label="{{ 'general.search.search' | t }}">
          <button type="submit" class="search-button">
            {{ 'general.search.submit' | t | default: 'Search' }}
          </button>
        </div>
      </form>
    </div>

    {% if search.performed %}
      {% if search.results_count > 0 %}
        <div class="search-results">
          {% for item in search.results %}
            {% case item.object_type %}
              {% when 'product' %}
                <div class="search-result search-result--product">
                  <a href="{{ item.url }}" class="search-result__link">
                    {% if item.featured_media %}
                      <div class="search-result__image">
                        {{ item.featured_media | image_url: width: 150 | image_tag: 
                           alt: item.featured_media.alt | default: item.title,
                           loading: 'lazy' }}
                      </div>
                    {% endif %}
                    
                    <div class="search-result__content">
                      <h3 class="search-result__title">{{ item.title }}</h3>
                      <div class="search-result__price">
                        {% if item.compare_at_price > item.price %}
                          <span class="price price--on-sale">{{ item.price | money }}</span>
                          <span class="price price--compare">{{ item.compare_at_price | money }}</span>
                        {% else %}
                          <span class="price">{{ item.price | money }}</span>
                        {% endif %}
                      </div>
                      {% if item.content != blank %}
                        <div class="search-result__excerpt">
                          {{ item.content | strip_html | truncate: 150 }}
                        </div>
                      {% endif %}
                    </div>
                  </a>
                </div>
              
              {% when 'page' %}
                <div class="search-result search-result--page">
                  <a href="{{ item.url }}" class="search-result__link">
                    <div class="search-result__content">
                      <h3 class="search-result__title">{{ item.title }}</h3>
                      <div class="search-result__type">Page</div>
                      {% if item.content != blank %}
                        <div class="search-result__excerpt">
                          {{ item.content | strip_html | truncate: 200 }}
                        </div>
                      {% endif %}
                    </div>
                  </a>
                </div>
              
              {% when 'article' %}
                <div class="search-result search-result--article">
                  <a href="{{ item.url }}" class="search-result__link">
                    <div class="search-result__content">
                      <h3 class="search-result__title">{{ item.title }}</h3>
                      <div class="search-result__type">Article</div>
                      {% if item.excerpt != blank %}
                        <div class="search-result__excerpt">
                          {{ item.excerpt | strip_html | truncate: 200 }}
                        </div>
                      {% endif %}
                    </div>
                  </a>
                </div>
            {% endcase %}
          {% endfor %}
        </div>
        
        {% if paginate.pages > 1 %}
          <nav class="pagination" role="navigation" aria-label="Pagination">
            {% if paginate.previous %}
              <a href="{{ paginate.previous.url }}" class="pagination__item pagination__item--prev">
                {{ 'general.pagination.previous' | t | default: 'Previous' }}
              </a>
            {% endif %}
            
            {% for part in paginate.parts %}
              {% if part.is_link %}
                <a href="{{ part.url }}" class="pagination__item">{{ part.title }}</a>
              {% else %}
                <span class="pagination__item pagination__item--current">{{ part.title }}</span>
              {% endif %}
            {% endfor %}
            
            {% if paginate.next %}
              <a href="{{ paginate.next.url }}" class="pagination__item pagination__item--next">
                {{ 'general.pagination.next' | t | default: 'Next' }}
              </a>
            {% endif %}
          </nav>
        {% endif %}
      {% else %}
        <div class="search-no-results">
          <h2>{{ 'templates.search.no_results' | t | default: 'No results found' }}</h2>
          <p>{{ 'templates.search.no_results_html' | t | default: 'Try adjusting your search terms or browse our collections.' }}</p>
          <a href="{{ routes.all_products_collection_url }}" class="button">
            {{ 'general.continue_shopping' | t | default: 'Browse Products' }}
          </a>
        </div>
      {% endif %}
    {% endif %}
  </div>
</div>

<style>
.template-search {
  padding: 40px 0;
}

.search-header {
  text-align: center;
  margin-bottom: 40px;
}

.search-title {
  font-size: 2.5rem;
  margin-bottom: 30px;
}

.search-form {
  max-width: 500px;
  margin: 0 auto;
}

.search-input-wrapper {
  display: flex;
  border: 2px solid #ddd;
  border-radius: 25px;
  overflow: hidden;
}

.search-input {
  flex: 1;
  padding: 12px 20px;
  border: none;
  outline: none;
  font-size: 1rem;
}

.search-button {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-button:hover {
  background-color: #0056b3;
}

.search-results {
  display: grid;
  gap: 20px;
  margin-top: 40px;
}

.search-result {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.search-result:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-result__link {
  display: flex;
  text-decoration: none;
  color: inherit;
  padding: 20px;
  gap: 20px;
}

.search-result__image {
  flex-shrink: 0;
  width: 100px;
}

.search-result__image img {
  width: 100%;
  height: auto;
  border-radius: 4px;
}

.search-result__content {
  flex: 1;
}

.search-result__title {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
}

.search-result__type {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-result__price {
  margin-bottom: 10px;
  font-weight: 600;
}

.price--on-sale {
  color: #e74c3c;
}

.price--compare {
  text-decoration: line-through;
  color: #999;
  margin-left: 10px;
}

.search-result__excerpt {
  color: #666;
  line-height: 1.5;
}

.search-no-results {
  text-align: center;
  padding: 60px 0;
}

.search-no-results h2 {
  margin-bottom: 20px;
  color: #333;
}

.search-no-results p {
  margin-bottom: 30px;
  color: #666;
}

.button {
  display: inline-block;
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.button:hover {
  background-color: #0056b3;
}
</style>
